{"litellm": {"model": "openai/gpt-4.1", "api_key": "a", "api_base": "http://lp00.home.kapdap.nz:45126/v1", "request_args": {"timeout": 300, "temperature": 0, "max_tokens": 20000}}, "marker": {"output_format": "markdown", "format_lines": false, "strip_existing_ocr": false, "force_ocr": false, "use_llm": false, "llm_service": "marker.services.openai.OpenAIService", "gemini_api_key": "AIzaSyDyhvFhbacdPqbxrKPuoS5dsQfyAx0egXU", "openai_base_url": "http://lp00.home.kapdap.nz:45126/v1", "openai_model": "gpt-4.1", "openai_api_key": "a", "vertex_project_id": "", "ollama_base_url": "http://localhost:11434", "ollama_model": "mistral:7b", "azure_endpoint": "", "azure_api_key": "", "deployment_name": ""}, "prompts": {"system_file": "prompts/system.md", "extract_file": "prompts/extract_invoice.md"}, "output": {"markdown_dir": "export/_markdown", "processed_dir": "export/_processed"}}