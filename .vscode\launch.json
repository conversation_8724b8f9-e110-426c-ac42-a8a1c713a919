{"version": "0.2.0", "configurations": [{"name": "Document Extractor - Process Samples", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/processer.py", "args": ["samples"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "justMyCode": true}, {"name": "Document Extractor - Custom Path", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/processer.py", "args": ["${input:customPath}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "justMyCode": true}], "inputs": [{"id": "customPath", "description": "Enter the path to PDF file(s) or folder(s)", "default": "samples", "type": "promptString"}]}