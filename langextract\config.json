{"langextract": {"model_id": "gemini-2.5-flash", "api_key": "", "extraction_passes": 1, "max_workers": 10, "max_char_buffer": 1000}, "marker": {"output_format": "markdown", "format_lines": false, "strip_existing_ocr": false, "force_ocr": false, "use_llm": false}, "prompts": {"system_file": "../prompts/system.md", "extract_file": "../prompts/extract_invoice.md"}, "output": {"markdown_dir": "export/_markdown", "processed_dir": "export/_processed"}}