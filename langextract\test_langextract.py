#!/usr/bin/env python3
"""
Test script for langextract processor

This simple test runs langextract on a sample text to verify the installation works correctly.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path to import langextract_processor
sys.path.insert(0, str(Path(__file__).parent))

from langextract_processor import get_langextract_examples, get_extraction_prompt, Config
import langextract as lx

def test_langextract():
    """Test langextract functionality with sample text."""
    
    print("Testing langextract functionality...")
    
    # Sample invoice text
    sample_text = """
    INVOICE
    
    ABC Company Ltd
    123 Business Street
    Auckland 1010, New Zealand
    GST: ***********
    
    BILL TO:
    Food and Dairy Systems Limited
    PO Box 59070
    Mangere Bridge, Auckland 2151
    GST: ***********
    
    Invoice #: INV-2024-001
    Date: 2024-01-15
    
    Description         Qty    Price      Total
    Test Product         1     $100.00    $100.00
    
    Subtotal:                             $100.00
    GST (15%):                            $15.00
    Total:                               $115.00
    """
    
    try:
        # Get examples and prompt
        examples = get_langextract_examples()
        prompt = get_extraction_prompt()
        
        print(f"Using {len(examples)} example(s)")
        print("Prompt length:", len(prompt))
        
        # Check if API key is available
        api_key = os.getenv("LANGEXTRACT_API_KEY")
        if not api_key:
            print("Warning: LANGEXTRACT_API_KEY not found in environment variables")
            print("Set LANGEXTRACT_API_KEY=your_api_key to run the full test")
            return
        
        print("Running langextract extraction...")
        
        # Run extraction
        result = lx.extract(
            text_or_documents=sample_text,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.5-flash",
            api_key=api_key,
        )
        
        # Handle the union return type - for single string input, it returns AnnotatedDocument
        if isinstance(result, lx.data.AnnotatedDocument):
            doc = result
        else:
            # If it's an iterable, take the first document
            doc = next(iter(result))
        
        print(f"Extraction successful!")
        print(f"Found {len(doc.extractions or [])} extractions")
        
        # Display some results
        for i, extraction in enumerate((doc.extractions or [])[:5]):
            print(f"  {i+1}. {extraction.extraction_class}: '{extraction.extraction_text}'")
        
        if doc.extractions and len(doc.extractions) > 5:
            print(f"  ... and {len(doc.extractions) - 5} more")
            
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_langextract()
