{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "isBackground": false, "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/ui"}}, {"label": "Start Development", "type": "shell", "command": "npm", "args": ["run", "tauri:dev"], "group": "build", "isBackground": true, "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/ui"}}, {"label": "Build Production", "type": "shell", "command": "npm", "args": ["run", "tauri:build"], "group": "build", "isBackground": false, "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/ui"}}]}