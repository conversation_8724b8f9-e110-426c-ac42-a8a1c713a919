#!/usr/bin/env python3
"""
PDF Structured Data Extractor using Marker and LiteLLM

This script processes PDF documents to extract structured data:
1. Converts PDFs to markdown using the marker library
2. Sends markdown to LiteLLM for structured data extraction
3. Saves results as JSON files
4. Supports configuration file for easy customization

Usage:
    python processor.py file1.pdf folder1/ file2.pdf folder2/
    python processor.py --config custom_config.json file1.pdf
"""

import argparse
import json
import os
import sys
from pathlib import Path
from textwrap import dedent
from typing import List, Dict, Any, Optional
import logging
from models import DocumentSchema

# LiteLLM imports
from litellm import completion
import instructor

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# =============================================================================
# Configuration Class
# =============================================================================


class Config:
    """Configuration manager for the data extraction process using LiteLLM."""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.json"
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
                return config
            else:
                logger.warning(
                    f"Config file {self.config_path} not found, using defaults"
                )
                return self._default_config()
        except Exception as e:
            logger.error(f"Failed to load config: {e}, using defaults")
            return self._default_config()

    def _default_config(self) -> Dict[str, Any]:
        """Return default configuration for LiteLLM."""
        return {
            "litellm": {
                "model": "openai/gpt-4.1",  # Can be any LiteLLM supported model
                "api_base": "",  # Optional: for custom endpoints
                "api_key": "",  # Uses environment variables if not provided
                "request_args": {
                    "timeout": 300,
                    "temperature": 0,
                    "max_tokens": 2000,
                },
                "config": {
                    "enable_json_schema_validation": True,
                },
            },
            "marker": {
                "output_format": "markdown",
                "format_lines": False,
                "strip_existing_ocr": False,
                "force_ocr": False,
                "use_llm": False,
            },
            "prompts": {
                "system_file": "prompts/system.md",
                "extract_file": "prompts/extract.md",
            },
            "output": {
                "markdown_dir": "samples/_markdown",
                "processed_dir": "samples/_processed",
            },
        }

    @property
    def litellm(self) -> Dict[str, Any]:
        return self.config.get("litellm", {})

    @property
    def marker(self) -> Dict[str, Any]:
        return self.config.get("marker", {})

    @property
    def prompts(self) -> Dict[str, Any]:
        return self.config.get("prompts", {})

    @property
    def output(self) -> Dict[str, Any]:
        return self.config.get("output", {})


# =============================================================================
# Core Functions
# =============================================================================


def setup_marker(app_config: Config):
    """Initialize marker converter with configuration."""
    try:
        from marker.converters.pdf import PdfConverter
        from marker.models import create_model_dict
        from marker.config.parser import ConfigParser
        from marker.output import text_from_rendered

        os.environ["TORCH_HOME"] = "./models_cache"
        os.environ["MARKER_CACHE_DIR"] = "./marker_cache"

        # Create configuration parser with settings from config
        marker_config = app_config.marker
        config_parser = ConfigParser(marker_config)

        # Create converter
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
            llm_service=config_parser.get_llm_service(),
        )

        return converter, text_from_rendered

    except ImportError as e:
        logger.error(f"Failed to import marker library: {e}")
        logger.error("Please install marker with: pip install marker-pdf")
        sys.exit(1)


def setup_litellm(config: Config):
    """Configure LiteLLM settings."""
    debug_enabled = config.litellm.get(
        "debug", os.getenv("LITELLM_DEBUG", "").lower() == "true"
    )

    if debug_enabled:
        # The correct way to enable verbose logging in LiteLLM
        os.environ["LITELLM_LOG"] = "DEBUG"
        logger.info("Enabled LiteLLM verbose logging")


def find_pdf_files(paths: List[str]) -> List[Path]:
    """
    Find all PDF files from the given list of file and folder paths.

    Args:
        paths: List of file and folder paths

    Returns:
        List of PDF file paths
    """
    pdf_files = []

    for path_str in paths:
        path = Path(path_str)

        if not path.exists():
            logger.warning(f"Path does not exist: {path}")
            continue

        if path.is_file():
            if path.suffix.lower() == ".pdf":
                pdf_files.append(path)
        elif path.is_dir():
            # Recursively find all PDF files in the directory
            found_pdfs = list(path.rglob("*.pdf"))
            pdf_files.extend(found_pdfs)

    return pdf_files


def convert_pdf_to_markdown(pdf_path: Path, converter, text_from_rendered) -> str:
    """
    Convert a PDF file to markdown using marker.

    Args:
        pdf_path: Path to the PDF file
        converter: Marker PdfConverter instance
        text_from_rendered: Marker text extraction function

    Returns:
        Markdown content as string
    """
    try:
        logger.info(f"Converting PDF to markdown: {pdf_path}")

        rendered = converter(str(pdf_path))

        text, metadata, images = text_from_rendered(rendered)

        logger.info(f"Successfully converted {pdf_path}")
        return text

    except Exception as e:
        logger.error(f"Failed to convert {pdf_path}: {e}")
        raise


def load_prompt_from_config(
    config: Config, prompt_type: str, default_prompt: str
) -> str:
    """
    Load a prompt from configuration, supporting both file-based and inline templates.

    Args:
        config: Configuration object containing the prompt settings
        prompt_type: Type of prompt to load (e.g., 'system', 'extract')
        default_prompt: Default prompt to use if none found in config

    Returns:
        The prompt string
    """
    prompt_file = config.prompts.get(f"{prompt_type}_file")
    if prompt_file:
        try:
            prompt_path = Path(prompt_file)
            if prompt_path.exists():
                with open(prompt_path, "r", encoding="utf-8") as f:
                    content = f.read()
                logger.info(f"Loaded {prompt_type} prompt from file: {prompt_file}")
                return content
            else:
                logger.warning(
                    f"Prompt file not found: {prompt_file}, falling back to template in config"
                )
        except Exception as e:
            logger.error(
                f"Failed to load prompt file {prompt_file}: {e}, falling back to template in config"
            )

    prompt_template = config.prompts.get(prompt_type, "")
    if not prompt_template:
        logger.warning(
            f"No {prompt_type} prompt template found in configuration, using default"
        )
        return default_prompt

    return prompt_template


def get_system_prompt(config: Config) -> str:
    """
    Get the system prompt from configuration.

    Args:
        config: Configuration object containing the prompt template

    Returns:
        The system prompt string
    """
    default = "You are a highly skilled AI assistant who is trained to extract structured data from documents. You will strictly follow the instructions described in the <instructions> tag to ensure accurate and consistent extraction of information. <document> tag contains the text from which you need to extract information. Use JSON format for the output."
    return load_prompt_from_config(config, "system", default)


def get_extract_prompt(config: Config) -> str:
    """
    Get the extract prompt from configuration.

    Args:
        config: Configuration object containing the prompt template

    Returns:
        The extract prompt string
    """
    default = """
# Data Extraction

You are to extract the relevant information from the given document and format it into a JSON object.

## Rules

- Only use values found in the document. Do not add any extra information that is not in the document.
"""
    return load_prompt_from_config(config, "extract", default)


def get_document_prompt(pdf_path: Path) -> str:
    """
    Get the document prompt from configuration.

    Args:
        document: The document to extract information from

    Returns:
        The document prompt string
    """
    default = ""

    try:
        category = get_document_category(pdf_path)
        path = Path(f"prompts/category/{category}.md")
        if path.exists():
            with open(path, "r", encoding="utf-8") as f:
                content = f.read()
            logger.info(f"Loaded document prompt from file: {path}")
            return content
    except Exception as e:
        logger.warning(f"Failed to get document prompt: {e}")

    return default


def get_document_category(pdf_path: Path) -> str:
    """
    Get the document category from the first child folder name.

    Args:
        document: The document to extract information from

    Returns:
        The document category string
    """
    category = ""

    try:
        parent = pdf_path.parent
        if parent.is_dir():
            category = parent.name
    except Exception as e:
        logger.warning(f"Failed to get document category: {e}")

    return category


def get_prompt_content(extract: str, document: str, content: str, filename: str) -> str:
    """
    Returns the prompt content with the prompt and content variables filled in.

    Args:
        extract: The extract prompt
        document: The document prompt
        content: The content of the document
        filename: The filename of the document

    Returns:
        The prompt content with the prompt and content variables filled in
    """
    return dedent(
        f"""
<extract_instructions>
{dedent(extract)}
</extract_instructions>
<document_instructions>
{dedent(document)}
</document_instructions>
<document_content>
{content}
</document_content>
<document_filename>
{filename}
</document_filename>
"""
    )


def set_litellm_api_key(api_key: str, model_name: str):
    """Set the API key environment variable for the specified model."""
    if model_name == "anthropic" or model_name == "claude":
        os.environ["ANTHROPIC_API_KEY"] = api_key
    elif model_name == "gemini" or model_name == "google":
        os.environ["GOOGLE_API_KEY"] = api_key
    elif model_name == "azure":
        os.environ["AZURE_API_KEY"] = api_key
    elif model_name == "cohere":
        os.environ["COHERE_API_KEY"] = api_key
    elif model_name == "groq":
        os.environ["GROQ_API_KEY"] = api_key
    else:
        os.environ["OPENAI_API_KEY"] = api_key


def call_litellm(content: str, pdf_path: Path, config: Config) -> Dict[str, Any]:
    """
    Call LiteLLM with Instructor for structured data extraction.

    Uses Instructor to automatically handle validation, retries, and structured output
    with any LiteLLM-supported provider.

    Args:
        content: The markdown content of the document extracted from the PDF
        pdf_path: Path to the PDF file
        config: Configuration object

    Returns:
        Structured JSON data validated by Pydantic
    """
    try:
        logger.info(
            f"Calling LiteLLM with Instructor, model: {config.litellm.get('model')}"
        )

        system = get_system_prompt(config)
        extract = get_extract_prompt(config)
        document = get_document_prompt(pdf_path)

        request_args: Dict[str, Any] = config.litellm.get("request_args", {})
        request_args.update({"model": config.litellm.get("model", "openapi/gpt-4.1")})
        request_args.update(
            {
                "messages": [
                    {"role": "system", "content": dedent(system)},
                    {
                        "role": "user",
                        "content": get_prompt_content(extract, document, content, pdf_path.name),
                    },
                ]
            }
        )

        api_base = config.litellm.get("api_base", "")
        if api_base:
            request_args.update({"api_base": api_base})

        api_key = config.litellm.get("api_key", "")
        if api_key:
            model_name = request_args.get("model", "").split("/")[0].lower()
            set_litellm_api_key(api_key, model_name)

        client = instructor.from_litellm(completion)

        response = client.chat.completions.create(
            response_model=DocumentSchema,  # TODO: Make schema configurable
            **request_args,
        )

        data = response.model_dump()
        logger.info("Successfully extracted structured data")

        return data

    except Exception as e:
        logger.error(f"LiteLLM API call failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        if hasattr(e, "__dict__"):
            logger.error(f"Error details: {e.__dict__}")
        raise


def load_markdown(md_path: Path) -> str:
    """Load markdown content from file."""
    if md_path.exists():
        with open(md_path, "r", encoding="utf-8") as f:
            return f.read()
    else:
        return ""


def save_markdown(content: str, output_path: Path) -> Path:
    """Save markdown content to file."""
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(content)
    logger.info(f"Saved markdown: {output_path}")
    return output_path


def save_json(data: Dict[str, Any], output_path: Path) -> Path:
    """Save JSON data to file."""
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    logger.info(f"Saved JSON: {output_path}")
    return output_path


def process_pdf(
    pdf_path: Path,
    converter,
    text_from_rendered,
    config: Config,
    markdown_dir: Path,
    processed_dir: Path,
):
    """Process a single PDF file through the complete pipeline."""
    try:
        logger.info(f"Processing: {pdf_path}")

        output_path = pdf_path.relative_to(pdf_path.parts[0]).parent
        markdown_path = markdown_dir / output_path / f"{pdf_path.stem}.md"
        processed_path = processed_dir / output_path / f"{pdf_path.stem}.json"

        content = load_markdown(markdown_path)
        if content == "":
            content = convert_pdf_to_markdown(pdf_path, converter, text_from_rendered)
            save_markdown(content, markdown_path)
        else:
            logger.info(f"Using existing markdown: {markdown_path}")

        response = call_litellm(content, pdf_path, config)

        save_json(response, processed_path)

        logger.info(f"Successfully processed: {pdf_path}")

    except Exception as e:
        logger.error(f"Failed to process {pdf_path}: {e}")


def main():
    """Main function to orchestrate the processing."""
    parser = argparse.ArgumentParser(
        description="Process PDF documents to extract structured data using marker and LiteLLM (supports 100+ LLM providers)"
    )
    parser.add_argument(
        "paths",
        nargs="+",
        help="List of PDF files and/or directories containing PDFs to process",
    )
    parser.add_argument(
        "--config", help="Path to JSON configuration file (default: config.json)"
    )

    args = parser.parse_args()

    config = Config(args.config)

    setup_litellm(config)

    output_config = config.output

    markdown_dir = Path(output_config.get("markdown_dir", "_markdown"))
    markdown_dir.mkdir(parents=True, exist_ok=True)

    processed_dir = Path(output_config.get("processed_dir", "_processed"))
    processed_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Output directories: {markdown_dir}, {processed_dir}")

    converter, text_from_rendered = setup_marker(config)

    pdf_files = find_pdf_files(args.paths)

    if not pdf_files:
        logger.warning("No PDF files found!")
        return

    logger.info(f"Found {len(pdf_files)} PDF files to process")

    successful = 0
    failed = 0

    for pdf_path in pdf_files:
        try:
            logger.info("---")
            process_pdf(
                pdf_path,
                converter,
                text_from_rendered,
                config,
                markdown_dir,
                processed_dir,
            )
            successful += 1
        except Exception as e:
            logger.error(f"Failed to process {pdf_path}: {e}")
            failed += 1

    logger.info("---")
    logger.info(
        f"Processing complete! Successfully processed: {successful}, Failed: {failed}"
    )


if __name__ == "__main__":
    main()
