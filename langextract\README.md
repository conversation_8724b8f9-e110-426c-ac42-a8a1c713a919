# LangExtract PDF Invoice Processor

This directory contains a PDF invoice processing system using Google's LangExtract library for structured data extraction. It converts PDFs to markdown using Marker and then extracts structured invoice data using LangExtract with comprehensive examples.

## Features

- **PDF to Markdown Conversion**: Uses the Marker library for high-quality PDF to markdown conversion
- **Structured Data Extraction**: Uses LangExtract with detailed examples for accurate invoice data extraction
- **Complete Invoice Schema**: Extracts all fields from the original models.py including:
  - Document metadata and AI notes
  - Supplier information (name, address, contact details, tax numbers)
  - Client information (name, address, contact details, tax numbers)
  - Delivery information (when present)
  - Invoice details (number, date, due date, payment terms)
  - Line items with quantities, prices, and amounts
  - Totals (subtotal, tax, final total)
- **Interactive Visualization**: Generates HTML visualizations showing extracted entities in context
- **Comprehensive Examples**: Includes detailed examples for different document types (invoices, receipts)

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your API key for Google Gemini:
```bash
export LANGEXTRACT_API_KEY="your-api-key-here"
```
Or create a `.env` file:
```
LANGEXTRACT_API_KEY=your-api-key-here
```

You can get a Gemini API key from [AI Studio](https://aistudio.google.com/app/apikey).

## Usage

### Basic Usage

Process a single PDF file:
```bash
python langextract_processor.py path/to/invoice.pdf
```

Process multiple PDF files:
```bash
python langextract_processor.py file1.pdf file2.pdf file3.pdf
```

Process all PDFs in a directory:
```bash
python langextract_processor.py path/to/pdf/directory/
```

### Configuration

The processor uses a configuration file (`config.json`) to control behavior:

```json
{
  "langextract": {
    "model_id": "gemini-2.5-flash",
    "extraction_passes": 1,
    "max_workers": 10,
    "max_char_buffer": 1000
  },
  "marker": {
    "output_format": "markdown",
    "force_ocr": false,
    "use_llm": false
  },
  "output": {
    "markdown_dir": "export/_markdown",
    "processed_dir": "export/_processed"
  }
}
```

Use a custom configuration file:
```bash
python langextract_processor.py --config my_config.json input.pdf
```

### Testing

Test the installation and API key:
```bash
python test_langextract.py
```

## Output

The processor generates several output files:

1. **Markdown files** (`export/_markdown/`): Converted PDF content
2. **JSON files** (`export/_processed/`): Structured invoice data matching the original schema
3. **JSONL files** (`export/_processed/visualizations/`): LangExtract format for visualization
4. **HTML files** (`export/_processed/visualizations/`): Interactive visualizations

### JSON Output Schema

The output JSON follows the exact schema from `models.py`:

```json
{
  "ai_notes": "Extraction notes and quality assessment",
  "type": "invoice",
  "supplier": {
    "name": "Company Name",
    "address": "Full address",
    "email": "<EMAIL>",
    "phone": "+64 9 123 4567",
    "tax_no": "***********",
    "tax_type": "GST"
  },
  "client": {
    "name": "Food and Dairy Systems Limited",
    "address": "PO Box 59070, Auckland 2151",
    "tax_no": "***********",
    "tax_type": "GST"
  },
  "details": {
    "date": "2024-01-15",
    "invoice_no": "INV-2024-001",
    "due_date": "2024-02-14",
    "payment_terms": "Net 30 days"
  },
  "items": [
    {
      "description": "Product description",
      "qty": 2,
      "unit_price": 100.00,
      "line_amount": 200.00,
      "tax_amount": 30.00,
      "discount_amount": 0.0
    }
  ],
  "totals": {
    "currency": "NZD",
    "subtotal": 200.00,
    "tax_total": 30.00,
    "total": 230.00
  }
}
```

## Key Advantages over Original Processor

1. **Better Entity Recognition**: LangExtract uses structured examples to guide extraction, leading to more consistent results
2. **Source Grounding**: Every extraction is tied to its exact location in the source text
3. **Interactive Visualization**: Generated HTML files allow visual inspection of extractions
4. **Flexible Examples**: Easy to add new document types by creating additional examples
5. **Robust Error Handling**: Better handling of malformed or unusual documents
6. **Multi-pass Extraction**: Optional multiple extraction passes for improved recall

## Examples

The system includes comprehensive examples covering:

- **New Zealand invoices** with GST formatting
- **Australian receipts** with ABN formatting  
- **Food and Dairy Systems** as client (both NZ and AU entities)
- **Multiple line items** with various attributes
- **Different document types** (invoices, receipts, delivery notes)

## Configuration Options

### LangExtract Settings

- `model_id`: Gemini model to use ("gemini-2.5-flash" recommended for speed, "gemini-2.5-pro" for complex documents)
- `extraction_passes`: Number of extraction attempts (1 = fast, 2-3 = better recall but more expensive)
- `max_workers`: Parallel processing workers (10 recommended)
- `max_char_buffer`: Text chunk size for processing (1000 recommended)

### Marker Settings

- `output_format`: Always "markdown" for this use case
- `force_ocr`: Force OCR even for digital PDFs (slower but sometimes more accurate)
- `use_llm`: Use LLM for improved table parsing (requires additional API costs)

## Performance Tips

1. **For large batches**: Increase `max_workers` and ensure `batch_length >= max_workers`
2. **For complex documents**: Use "gemini-2.5-pro" model and set `extraction_passes=2`
3. **For cost optimization**: Use "gemini-2.5-flash" and `extraction_passes=1`
4. **For OCR-heavy documents**: Set `force_ocr=true` in marker config

## Troubleshooting

### Common Issues

1. **"API key not found"**: Set the LANGEXTRACT_API_KEY environment variable
2. **"Marker import error"**: Install marker-pdf: `pip install marker-pdf`
3. **Poor extraction quality**: Try increasing `extraction_passes` or using "gemini-2.5-pro"
4. **Rate limits**: Reduce `max_workers` or get a Tier 2 Gemini quota

### Debugging

Enable debug output by setting the debug flag in the configuration or running with verbose logging.

## Cost Considerations

LangExtract uses cloud APIs which incur costs:
- Text processing: ~$0.01-0.05 per page depending on model and content
- Multiple extraction passes multiply the cost
- Monitor usage with small test runs before processing large batches

## License

This implementation follows the same license as the parent project.
