from enum import Enum
from typing import Optional, List
from pydantic import BaseModel

class DocumentType(str, Enum):
    invoice = "invoice"
    receipt = "receipt"
    delivery_note = "delivery_note"
    purchase_order = "purchase_order"
    credit_note = "credit_note"

class Address(BaseModel):
    line1: Optional[str] = None
    line2: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    notes: Optional[str] = None

class BankDetails(BaseModel):
    branch_name: Optional[str] = None
    branch_address: Optional[str] = None
    account_name: Optional[str] = None
    account_bsb: Optional[str] = None
    account_no: Optional[str] = None
    account_swift: Optional[str] = None
    full_details: Optional[str] = None

class Party(BaseModel):
    name: str
    address: Optional[str] = None
    contact: Optional[str] = None
    care_of: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    business_no: Optional[str] = None
    tax_no: Optional[str] = None
    tax_type: Optional[str] = None
    bank: Optional[BankDetails] = None
    notes: Optional[str] = None

class Delivery(BaseModel):
    name: Optional[str] = None
    contact: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    
class InvoiceTotals(BaseModel):
    currency: str
    subtotal: float
    tax_total: float
    total: float
    notes: Optional[str] = None
    
class InvoiceDetails(BaseModel):
    date: str
    due_date: Optional[str] = None
    invoice_no: str
    order_no: Optional[str] = None
    client_no: Optional[str] = None
    payment_terms: Optional[str] = None
    notes: Optional[str] = None

class InvoiceItem(BaseModel):
    code: Optional[str]
    description: str
    discount_amount: float
    line_amount: float
    qty: int
    tax_amount: float
    unit_price: float
    uom: Optional[str] = None
    informational: Optional[bool] = False
    notes: Optional[str] = None

class DocumentSchema(BaseModel):
    ai_notes: str
    client: Party
    supplier: Party
    delivery: Optional[Delivery] = None
    details: InvoiceDetails
    items: List[InvoiceItem]
    totals: InvoiceTotals
    type: DocumentType