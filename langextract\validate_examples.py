#!/usr/bin/env python3
"""
Simple validation script to test if langextract examples are correctly formatted.
"""

import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

# Import and test the processor
from langextract_processor import get_langextract_examples

def main():
    print("Testing langextract examples format...")
    
    try:
        examples = get_langextract_examples()
        print(f"✓ Successfully created {len(examples)} examples")
        
        for i, example in enumerate(examples, 1):
            print(f"\nExample {i}:")
            print(f"  - Text length: {len(example.text)} characters")
            print(f"  - Number of extractions: {len(example.extractions)}")
            
            # Check extraction format
            for j, extraction in enumerate(example.extractions[:3]):  # Show first 3
                print(f"    Extraction {j+1}: class='{extraction.extraction_class}', text='{extraction.extraction_text[:50]}...'")
                if hasattr(extraction, 'attributes') and extraction.attributes:
                    print(f"      Attributes: {list(extraction.attributes.keys())}")
        
        print("\n✓ All examples have correct format!")
        return True
        
    except Exception as e:
        print(f"✗ Error creating examples: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
