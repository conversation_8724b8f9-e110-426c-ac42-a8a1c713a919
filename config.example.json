{"litellm": {"model": "openai/gpt-4.1", "api_key": "", "api_base": "", "request_args": {"timeout": 300, "temperature": 0, "max_tokens": 2000}}, "marker": {"output_format": "markdown", "strip_existing_ocr": false, "format_lines": false, "force_ocr": false, "use_llm": false, "llm_service": "", "gemini_api_key": "", "openai_base_url": "", "openai_model": "gpt-4.1", "openai_api_key": "", "vertex_project_id": "", "ollama_base_url": "http://localhost:11434", "ollama_model": "mistral:7b", "azure_endpoint": "", "azure_api_key": "", "deployment_name": ""}, "prompts": {"system_file": "prompts/system.md", "extraction_file": "prompts/extract_invoice.md"}, "output": {"markdown_dir": "samples/_markdown", "processed_dir": "samples/_processed"}}