#!/usr/bin/env python3
"""
PDF Structured Data Extractor using LangExtract

This script processes PDF documents to extract structured data using the langextract library:
1. Converts PDFs to markdown using the marker library
2. Uses langextract with structured examples to extract invoice data
3. Saves results as JSON files
4. Supports configuration file for easy customization

Usage:
    python langextract_processor.py file1.pdf folder1/ file2.pdf folder2/
    python langextract_processor.py --config custom_config.json file1.pdf
"""

import argparse
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

import langextract as lx

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# =============================================================================
# Configuration Class
# =============================================================================

class Config:
    """Configuration manager for the langextract data extraction process."""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.json"
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found. Using defaults.")
            return self._default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._default_config()

    def _default_config(self) -> Dict[str, Any]:
        """Return default configuration for LangExtract."""
        return {
            "langextract": {
                "model_id": "gemini-2.5-flash",
                "api_key": os.getenv("LANGEXTRACT_API_KEY", ""),
                "extraction_passes": 1,
                "max_workers": 10,
                "max_char_buffer": 1000,
            },
            "marker": {
                "output_format": "markdown",
                "format_lines": False,
                "strip_existing_ocr": False,
                "force_ocr": False,
                "use_llm": False,
            },
            "prompts": {
                "system_file": "prompts/system.md",
                "extract_file": "prompts/extract_invoice.md",
            },
            "output": {
                "markdown_dir": "export/_markdown",
                "processed_dir": "export/_processed",
            },
        }

    @property
    def langextract(self) -> Dict[str, Any]:
        return self.config.get("langextract", {})

    @property
    def marker(self) -> Dict[str, Any]:
        return self.config.get("marker", {})

    @property
    def prompts(self) -> Dict[str, Any]:
        return self.config.get("prompts", {})

    @property
    def output(self) -> Dict[str, Any]:
        return self.config.get("output", {})


# =============================================================================
# Example Data for LangExtract
# =============================================================================

def get_langextract_examples() -> List[lx.data.ExampleData]:
    """Create comprehensive langextract examples with complete invoice structure."""
    
    examples = [
        lx.data.ExampleData(
            text="INVOICE\n\nABC Manufacturing Ltd\n123 Industrial Way\nAuckland 1010, New Zealand\nPhone: +64 9 123 4567\nEmail: <EMAIL>\nGST: ***********\n\nBILL TO:\nFood and Dairy Systems Limited\nPO Box 59070\nMangere Bridge, Auckland 2151\nNew Zealand\nGST: ***********\n\nInvoice #: INV-2024-001\nDate: 2024-01-15\nDue Date: 2024-02-14\n\nDescription                 Qty    Unit Price    Amount\nIndustrial Pump Model X100    2      $2,500.00   $5,000.00\nInstallation Service          1      $750.00     $750.00\n\nSubtotal:                                        $5,750.00\nGST (15%):                                       $862.50\nTotal:                                          $6,612.50\n\nPayment Terms: Net 30 days",
            extractions=[
                lx.data.Extraction(extraction_class="supplier", extraction_text="ABC Manufacturing Ltd", attributes={"name": "ABC Manufacturing Ltd", "address": "123 Industrial Way, Auckland 1010, New Zealand", "phone": "+64 9 123 4567", "email": "<EMAIL>", "tax_no": "***********", "tax_type": "GST"}),
                lx.data.Extraction(extraction_class="client", extraction_text="Food and Dairy Systems Limited", attributes={"name": "Food and Dairy Systems Limited", "address": "PO Box 59070, Mangere Bridge, Auckland 2151, New Zealand", "tax_no": "***********", "tax_type": "GST"}),
                lx.data.Extraction(extraction_class="invoice", extraction_text="INV-2024-001", attributes={"invoice_no": "INV-2024-001", "date": "2024-01-15", "due_date": "2024-02-14", "payment_terms": "Net 30 days", "type": "invoice"}),
                lx.data.Extraction(extraction_class="item", extraction_text="Industrial Pump Model X100", attributes={"description": "Industrial Pump Model X100", "qty": "2", "unit_price": "2500.00", "line_amount": "5000.00", "item_group": "item_1"}),
                lx.data.Extraction(extraction_class="item", extraction_text="Installation Service", attributes={"description": "Installation Service", "qty": "1", "unit_price": "750.00", "line_amount": "750.00", "item_group": "item_2"}),
                lx.data.Extraction(extraction_class="totals", extraction_text="$6,612.50", attributes={"subtotal": "5750.00", "tax_total": "862.50", "total": "6612.50", "currency": "NZD", "tax_rate": "15%"}),
            ]
        ),
        
        lx.data.ExampleData(
            text="RECEIPT\n\nXYZ Services Pty Ltd\n456 Business Street\nSydney NSW 2000, Australia\nABN: **************\nPhone: +61 2 9876 5432\n\nCUSTOMER:\nFood and Dairy Systems Australia Pty Ltd\nLevel 5, 100 George Street\nSydney NSW 2000, Australia\nABN: **************\n\nReceipt No: REC-2024-100\nDate: 2024-02-20\n\nItem                        Quantity    Price       Total\nConsulting Services              5     $200.00    $1,000.00\nTravel Expenses                  1     $150.00    $150.00\n\nNet Amount:                                        $1,150.00\nGST (10%):                                         $115.00\nTotal Amount:                                      $1,265.00",
            extractions=[
                lx.data.Extraction(extraction_class="supplier", extraction_text="XYZ Services Pty Ltd", attributes={"name": "XYZ Services Pty Ltd", "address": "456 Business Street, Sydney NSW 2000, Australia", "business_no": "**************", "phone": "+61 2 9876 5432"}),
                lx.data.Extraction(extraction_class="client", extraction_text="Food and Dairy Systems Australia Pty Ltd", attributes={"name": "Food and Dairy Systems Australia Pty Ltd", "address": "Level 5, 100 George Street, Sydney NSW 2000, Australia", "business_no": "**************"}),
                lx.data.Extraction(extraction_class="receipt", extraction_text="REC-2024-100", attributes={"receipt_no": "REC-2024-100", "date": "2024-02-20", "type": "receipt"}),
                lx.data.Extraction(extraction_class="item", extraction_text="Consulting Services", attributes={"description": "Consulting Services", "qty": "5", "unit_price": "200.00", "line_amount": "1000.00", "item_group": "item_1"}),
                lx.data.Extraction(extraction_class="item", extraction_text="Travel Expenses", attributes={"description": "Travel Expenses", "qty": "1", "unit_price": "150.00", "line_amount": "150.00", "item_group": "item_2"}),
                lx.data.Extraction(extraction_class="totals", extraction_text="$1,265.00", attributes={"subtotal": "1150.00", "tax_total": "115.00", "total": "1265.00", "currency": "AUD", "tax_rate": "10%"}),
            ]
        )
    ]
    
    return examples


def get_extraction_prompt() -> str:
    """Get the extraction prompt for langextract."""
    return """Extract structured invoice/receipt information from the document text.

Extract the following entities in the order they appear:

1. Document information:
   - document_type: The type of document (invoice, receipt, etc.)
   - ai_notes: Summary of extraction quality and any issues

2. Supplier information (the company issuing the document):
   - supplier_name: Company name
   - supplier_address: Full address
   - supplier_contact: Contact person name
   - supplier_email: Email address
   - supplier_phone: Phone number
   - supplier_business_no: Business registration number
   - supplier_tax_no: Tax registration number
   - supplier_bank_details: Banking information

3. Client information (the company being billed):
   - client_name: Company name
   - client_address: Full address
   - client_contact: Contact person name
   - client_email: Email address
   - client_phone: Phone number
   - client_business_no: Business registration number
   - client_tax_no: Tax registration number

4. Delivery information (if present):
   - delivery_name: Delivery contact name
   - delivery_address: Delivery address
   - delivery_contact: Contact person
   - delivery_phone: Phone number
   - delivery_email: Email address

5. Document details:
   - invoice_no/receipt_no: Document number
   - invoice_date/receipt_date: Document date
   - due_date: Payment due date
   - order_no: Purchase order number
   - client_no: Client reference number
   - payment_terms: Payment terms

6. Line items (use item_group attribute to link related information):
   - item_description: Description of goods/services
   - item_code: Item/product code
   - item_qty: Quantity
   - item_unit_price: Price per unit
   - item_line_amount: Total line amount
   - item_tax_amount: Tax amount for line
   - item_discount_amount: Discount amount
   - item_uom: Unit of measure

7. Totals:
   - subtotal: Subtotal before tax
   - tax_total: Total tax amount
   - total: Final total amount

Use attributes to provide additional context:
- entity_type: "supplier", "client", "delivery"
- field_type: "details", "totals", "items"
- item_group: "item_1", "item_2", etc. for grouping line item details
- currency: Currency code (e.g., "NZD", "AUD", "USD")
- tax_type: Type of tax (e.g., "GST", "VAT")
- amount_type: "subtotal", "tax", "total"

Extract exact text from the document. Do not paraphrase or modify the content."""


# =============================================================================
# Core Functions
# =============================================================================

def setup_marker(config: Config):
    """Initialize marker converter with configuration."""
    try:
        from marker.converters.pdf import PdfConverter
        from marker.renderers.markdown import MarkdownRenderer
        
        # Configure marker settings
        marker_config = config.marker
        
        # Create artifact dict for marker
        artifact_dict = {}
        
        # Configure marker
        converter = PdfConverter(artifact_dict=artifact_dict)
        renderer = MarkdownRenderer()
        
        return converter, renderer
    except ImportError as e:
        logger.error(f"Failed to import marker: {e}")
        logger.error("Please install marker: pip install marker-pdf")
        sys.exit(1)


def find_pdf_files(paths: List[str]) -> List[Path]:
    """Find all PDF files from the given list of file and folder paths."""
    pdf_files = []
    
    for path_str in paths:
        path = Path(path_str)
        if path.is_file() and path.suffix.lower() == '.pdf':
            pdf_files.append(path)
        elif path.is_dir():
            pdf_files.extend(path.rglob('*.pdf'))
        else:
            logger.warning(f"Path not found or not a PDF: {path}")
    
    return pdf_files


def convert_pdf_to_markdown(pdf_path: Path, converter, renderer) -> str:
    """Convert a PDF file to markdown using marker."""
    try:
        logger.info(f"Converting PDF to markdown: {pdf_path}")
        
        # Convert PDF to structured document
        doc_result = converter(str(pdf_path))
        
        # Render as markdown
        markdown_content = renderer(doc_result)
        
        return markdown_content
    except Exception as e:
        logger.error(f"Error converting PDF {pdf_path}: {e}")
        raise


def extract_with_langextract(content: str, config: Config):
    """Extract structured data using langextract."""
    try:
        logger.info("Extracting structured data with langextract")
        
        # Get configuration
        langextract_config = config.langextract
        
        # Get examples and prompt
        examples = get_langextract_examples()
        prompt = get_extraction_prompt()
        
        # Run extraction
        result = lx.extract(
            text_or_documents=content,
            prompt_description=prompt,
            examples=examples,
            model_id=langextract_config.get("model_id", "gemini-2.5-flash"),
            api_key=langextract_config.get("api_key") or os.getenv("LANGEXTRACT_API_KEY"),
            extraction_passes=langextract_config.get("extraction_passes", 1),
            max_workers=langextract_config.get("max_workers", 10),
            max_char_buffer=langextract_config.get("max_char_buffer", 1000),
        )
        
        # Handle the union return type - for single string input, it returns AnnotatedDocument
        if isinstance(result, lx.data.AnnotatedDocument):
            return result
        else:
            # If it's an iterable, take the first document
            return next(iter(result))
    except Exception as e:
        logger.error(f"Error in langextract extraction: {e}")
        raise


def convert_langextract_to_json(result: lx.data.AnnotatedDocument) -> Dict[str, Any]:
    """Convert langextract result to our JSON schema format."""
    
    # Initialize the result structure
    json_result = {
        "ai_notes": "",
        "type": "invoice",
        "supplier": {
            "name": "",
            "address": None,
            "contact": None, 
            "care_of": None,
            "email": None,
            "phone": None,
            "business_no": None,
            "tax_no": None,
            "tax_type": None,
            "bank": None,
            "notes": None
        },
        "client": {
            "name": "",
            "address": None,
            "contact": None,
            "care_of": None, 
            "email": None,
            "phone": None,
            "business_no": None,
            "tax_no": None,
            "tax_type": None,
            "bank": None,
            "notes": None
        },
        "delivery": None,
        "details": {
            "date": "",
            "due_date": None,
            "invoice_no": "",
            "order_no": None,
            "client_no": None,
            "payment_terms": None,
            "notes": None
        },
        "items": [],
        "totals": {
            "currency": "NZD",
            "subtotal": 0.0,
            "tax_total": 0.0,
            "total": 0.0,
            "notes": None
        }
    }
    
    # Group items by item_group
    item_groups = {}
    
    # Process extractions
    for extraction in result.extractions or []:
        class_name = extraction.extraction_class
        text = extraction.extraction_text
        attrs = extraction.attributes or {}
        
        # Document type and AI notes
        if class_name == "document_type":
            if "invoice" in text.lower():
                json_result["type"] = "invoice"
            elif "receipt" in text.lower():
                json_result["type"] = "receipt"
            elif "delivery" in text.lower():
                json_result["type"] = "delivery_note"
                
        elif class_name == "ai_notes":
            json_result["ai_notes"] = text
            
        # Supplier information
        elif class_name.startswith("supplier_"):
            field = class_name[9:]  # Remove "supplier_" prefix
            if field == "name":
                json_result["supplier"]["name"] = text
            elif field == "address":
                json_result["supplier"]["address"] = text
            elif field == "contact":
                json_result["supplier"]["contact"] = text
            elif field == "email":
                json_result["supplier"]["email"] = text
            elif field == "phone":
                json_result["supplier"]["phone"] = text
            elif field == "business_no":
                json_result["supplier"]["business_no"] = text
            elif field == "tax_no":
                json_result["supplier"]["tax_no"] = text
                json_result["supplier"]["tax_type"] = attrs.get("tax_type", "GST")
                
        # Client information
        elif class_name.startswith("client_"):
            field = class_name[7:]  # Remove "client_" prefix
            if field == "name":
                json_result["client"]["name"] = text
            elif field == "address":
                json_result["client"]["address"] = text
            elif field == "contact":
                json_result["client"]["contact"] = text
            elif field == "email":
                json_result["client"]["email"] = text
            elif field == "phone":
                json_result["client"]["phone"] = text
            elif field == "business_no":
                json_result["client"]["business_no"] = text
            elif field == "tax_no":
                json_result["client"]["tax_no"] = text
                json_result["client"]["tax_type"] = attrs.get("tax_type", "GST")
                
        # Invoice/Receipt details
        elif class_name in ["invoice_no", "receipt_no"]:
            json_result["details"]["invoice_no"] = text
        elif class_name in ["invoice_date", "receipt_date"]:
            json_result["details"]["date"] = text
        elif class_name == "due_date":
            json_result["details"]["due_date"] = text
        elif class_name == "order_no":
            json_result["details"]["order_no"] = text
        elif class_name == "client_no":
            json_result["details"]["client_no"] = text
        elif class_name == "payment_terms":
            json_result["details"]["payment_terms"] = text
            
        # Line items
        elif class_name == "item_description":
            item_group = attrs.get("item_group", "default")
            if item_group not in item_groups:
                item_groups[item_group] = {
                    "code": None,
                    "description": text,
                    "discount_amount": 0.0,
                    "line_amount": 0.0,
                    "qty": 1,
                    "tax_amount": 0.0,
                    "unit_price": 0.0,
                    "uom": None,
                    "informational": False,
                    "notes": None
                }
            else:
                item_groups[item_group]["description"] = text
                
            # Extract other item details from attributes
            if "qty" in attrs and isinstance(attrs["qty"], str):
                try:
                    item_groups[item_group]["qty"] = int(attrs["qty"])
                except ValueError:
                    pass
            if "unit_price" in attrs and isinstance(attrs["unit_price"], str):
                try:
                    item_groups[item_group]["unit_price"] = float(attrs["unit_price"])
                except ValueError:
                    pass
            if "line_amount" in attrs and isinstance(attrs["line_amount"], str):
                try:
                    item_groups[item_group]["line_amount"] = float(attrs["line_amount"])
                except ValueError:
                    pass
                    
        # Totals
        elif class_name == "subtotal":
            try:
                # Extract numeric value from text like "$5,750.00"
                numeric_value = ''.join(c for c in text if c.isdigit() or c == '.')
                if numeric_value:
                    json_result["totals"]["subtotal"] = float(numeric_value)
                if "currency" in attrs:
                    json_result["totals"]["currency"] = attrs["currency"]
            except ValueError:
                pass
                
        elif class_name == "tax_total":
            try:
                numeric_value = ''.join(c for c in text if c.isdigit() or c == '.')
                if numeric_value:
                    json_result["totals"]["tax_total"] = float(numeric_value)
            except ValueError:
                pass
                
        elif class_name == "total":
            try:
                numeric_value = ''.join(c for c in text if c.isdigit() or c == '.')
                if numeric_value:
                    json_result["totals"]["total"] = float(numeric_value)
            except ValueError:
                pass
    
    # Convert item groups to items list
    json_result["items"] = list(item_groups.values())
    
    # Set default AI notes if empty
    if not json_result["ai_notes"]:
        json_result["ai_notes"] = "Extracted using langextract with structured examples"
    
    return json_result


def save_markdown(content: str, output_path: Path) -> Path:
    """Save markdown content to file."""
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(content)
    logger.info(f"Saved markdown: {output_path}")
    return output_path


def save_json(data: Dict[str, Any], output_path: Path) -> Path:
    """Save JSON data to file."""
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    logger.info(f"Saved JSON: {output_path}")
    return output_path


def process_pdf(
    pdf_path: Path,
    converter,
    renderer,
    config: Config,
    markdown_dir: Path,
    processed_dir: Path,
):
    """Process a single PDF file through the complete pipeline."""
    try:
        logger.info(f"Processing: {pdf_path}")
        
        # Generate output filenames
        base_name = pdf_path.stem
        markdown_path = markdown_dir / f"{base_name}.md"
        json_path = processed_dir / f"{base_name}.json"
        
        # Skip if already processed
        if json_path.exists():
            logger.info(f"Already processed: {json_path}")
            return
        
        # Convert PDF to markdown
        markdown_content = convert_pdf_to_markdown(pdf_path, converter, renderer)
        save_markdown(markdown_content, markdown_path)
        
        # Extract structured data with langextract
        langextract_result = extract_with_langextract(markdown_content, config)
        
        # Convert to our JSON schema
        json_data = convert_langextract_to_json(langextract_result)
        
        # Save JSON result
        save_json(json_data, json_path)
        
        # Save langextract visualization
        viz_dir = processed_dir / "visualizations"
        viz_dir.mkdir(exist_ok=True)
        jsonl_path = viz_dir / f"{base_name}.jsonl"
        
        # Save as JSONL for visualization
        lx.io.save_annotated_documents(iter([langextract_result]), 
                                     output_name=jsonl_path.name, 
                                     output_dir=str(viz_dir))
        
        # Generate HTML visualization
        html_content = lx.visualize(str(jsonl_path))
        html_path = viz_dir / f"{base_name}.html"
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        logger.info(f"Successfully processed: {pdf_path}")
        logger.info(f"Visualization saved: {html_path}")
        
    except Exception as e:
        logger.error(f"Error processing {pdf_path}: {e}")
        raise


def main():
    """Main function to orchestrate the processing."""
    parser = argparse.ArgumentParser(
        description="Process PDF documents to extract structured data using marker and langextract"
    )
    parser.add_argument(
        "paths",
        nargs="+",
        help="List of PDF files and/or directories containing PDFs to process",
    )
    parser.add_argument(
        "--config", help="Path to JSON configuration file (default: config.json)"
    )

    args = parser.parse_args()

    # Load configuration
    config = Config(args.config)

    # Setup output directories
    output_config = config.output
    markdown_dir = Path(output_config.get("markdown_dir", "_markdown"))
    markdown_dir.mkdir(parents=True, exist_ok=True)

    processed_dir = Path(output_config.get("processed_dir", "_processed"))
    processed_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Output directories: {markdown_dir}, {processed_dir}")

    # Setup marker
    converter, renderer = setup_marker(config)

    # Find PDF files
    pdf_files = find_pdf_files(args.paths)

    if not pdf_files:
        logger.error("No PDF files found to process")
        return

    logger.info(f"Found {len(pdf_files)} PDF files to process")

    # Process files
    successful = 0
    failed = 0

    for pdf_path in pdf_files:
        try:
            process_pdf(pdf_path, converter, renderer, config, markdown_dir, processed_dir)
            successful += 1
        except Exception as e:
            logger.error(f"Failed to process {pdf_path}: {e}")
            failed += 1

    logger.info("---")
    logger.info(
        f"Processing complete! Successfully processed: {successful}, Failed: {failed}"
    )


if __name__ == "__main__":
    main()
